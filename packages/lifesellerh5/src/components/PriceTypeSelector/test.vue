<template>
  <div class="test-page">
    <h1>价格类型选择组件测试</h1>
    
    <div class="test-section">
      <h2>基础测试</h2>
      <div class="test-item">
        <label>价格类型：</label>
        <PriceTypeSelector v-model="priceType" />
        <span class="result">当前值：{{ priceType }}</span>
      </div>
    </div>

    <div class="test-section">
      <h2>功能验证</h2>
      <ul>
        <li>✅ 默认显示"元"文字</li>
        <li>✅ 点击展开浮层选择器</li>
        <li>✅ 显示两个选项：固定价(元)、起售价(元起)</li>
        <li>✅ 选中项显示对勾图标</li>
        <li>✅ 支持 v-model 双向绑定</li>
        <li>✅ 点击外部区域关闭浮层</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>数据验证</h2>
      <div class="data-display">
        <p>当前选择的价格类型：{{ priceType }}</p>
        <p>对应的显示文字：{{ getDisplayText(priceType) }}</p>
        <p>对应的类型名称：{{ getTypeName(priceType) }}</p>
      </div>
    </div>

    <div class="test-section">
      <h2>切换测试</h2>
      <div class="button-group">
        <button @click="setPriceType(PriceType.FIXED)">设置为固定价</button>
        <button @click="setPriceType(PriceType.STARTING)">设置为起售价</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PriceTypeSelector from './index.vue'
import { PriceType } from './types'

const priceType = ref<number>(PriceType.FIXED)

const getDisplayText = (type: number): string => {
  return type === PriceType.FIXED ? '元' : '元起'
}

const getTypeName = (type: number): string => {
  return type === PriceType.FIXED ? '固定价' : '起售价'
}

const setPriceType = (type: PriceType) => {
  priceType.value = type
}
</script>

<style scoped lang="stylus">
.test-page
  padding 20px
  max-width 600px
  margin 0 auto
  font-family -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif

h1
  color #333
  text-align center
  margin-bottom 30px

.test-section
  margin-bottom 30px
  padding 20px
  background-color #f9f9f9
  border-radius 8px

h2
  color #333
  font-size 16px
  margin-bottom 15px

.test-item
  display flex
  align-items center
  gap 12px

label
  min-width 80px
  font-size 14px
  color #666

.result
  font-size 14px
  color #999

ul
  margin 0
  padding-left 20px

li
  margin-bottom 8px
  font-size 14px
  color #333

.data-display
  p
    margin 8px 0
    font-size 14px
    color #333

.button-group
  display flex
  gap 12px

button
  padding 8px 16px
  background-color #1890ff
  color white
  border none
  border-radius 4px
  cursor pointer
  font-size 14px

  &:hover
    background-color #40a9ff

  &:active
    background-color #096dd9
</style>
