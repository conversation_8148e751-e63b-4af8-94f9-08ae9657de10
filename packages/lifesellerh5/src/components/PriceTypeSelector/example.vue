<template>
  <div class="example-page">
    <div class="header">
      <h1>价格类型选择组件示例</h1>
    </div>

    <div class="demo-section">
      <h2>基础用法</h2>
      <div class="demo-item">
        <label>价格类型：</label>
        <PriceTypeSelector v-model="basicPriceType" />
        <span class="result">当前值：{{ basicPriceType }} ({{ getPriceTypeText(basicPriceType) }})</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>表单中使用</h2>
      <div class="form-demo">
        <div class="form-item">
          <label>商品名称：</label>
          <input v-model="formData.name" type="text" placeholder="请输入商品名称" />
        </div>

        <div class="form-item">
          <label>价格类型：</label>
          <PriceTypeSelector v-model="formData.priceType" />
        </div>

        <div class="form-item">
          <label>价格：</label>
          <input v-model="formData.price" type="number" placeholder="请输入价格" />
          <span class="unit">{{ getPriceUnit(formData.priceType) }}</span>
        </div>

        <div class="form-result">
          <h3>表单数据：</h3>
          <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>多个实例</h2>
      <div class="multiple-demo">
        <div class="demo-item">
          <label>商品A价格类型：</label>
          <PriceTypeSelector v-model="productA.priceType" />
          <span class="result">{{ getPriceTypeText(productA.priceType) }}</span>
        </div>

        <div class="demo-item">
          <label>商品B价格类型：</label>
          <PriceTypeSelector v-model="productB.priceType" />
          <span class="result">{{ getPriceTypeText(productB.priceType) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import PriceTypeSelector from './index.vue'
import { PriceType } from './types'

// 基础用法
const basicPriceType = ref<number>(PriceType.FIXED)

// 表单用法
const formData = reactive({
  name: '',
  priceType: PriceType.FIXED,
  price: 0
})

// 多个实例
const productA = reactive({
  priceType: PriceType.FIXED
})

const productB = reactive({
  priceType: PriceType.STARTING
})

// 辅助方法
const getPriceTypeText = (type: number): string => {
  return type === PriceType.FIXED ? '固定价' : '起售价'
}

const getPriceUnit = (type: number): string => {
  return type === PriceType.FIXED ? '元' : '元起'
}
</script>

<style scoped lang="stylus">
.example-page
  padding 20px
  max-width 800px
  margin 0 auto
  font-family -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif

.header
  text-align center
  margin-bottom 40px

  h1
    color #333
    font-size 24px
    font-weight 500

.demo-section
  margin-bottom 40px
  padding 20px
  background-color #f9f9f9
  border-radius 8px

  h2
    color #333
    font-size 18px
    font-weight 500
    margin-bottom 20px

.demo-item
  display flex
  align-items center
  margin-bottom 16px

  label
    min-width 120px
    font-size 14px
    color #666

  .result
    margin-left 16px
    font-size 14px
    color #999

.form-demo
  .form-item
    display flex
    align-items center
    margin-bottom 16px

    label
      min-width 100px
      font-size 14px
      color #666

    input
      flex 1
      padding 8px 12px
      border 1px solid #e6e6e6
      border-radius 4px
      font-size 14px
      margin-right 8px

      &:focus
        outline none
        border-color #1890ff

    .unit
      font-size 14px
      color #999
      margin-left 8px

.form-result
  margin-top 20px
  padding 16px
  background-color #fff
  border-radius 4px
  border 1px solid #e6e6e6

  h3
    margin 0 0 12px 0
    font-size 14px
    color #333

  pre
    margin 0
    font-size 12px
    color #666
    white-space pre-wrap
    word-break break-all

.multiple-demo
  .demo-item
    margin-bottom 20px
</style>
