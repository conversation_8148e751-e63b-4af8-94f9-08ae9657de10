<template>
  <div class="price-type-selector">
    <!-- 默认显示区域 -->
    <div class="selector-trigger" @click="toggleDropdown">
      <span class="display-text">{{ displayText }}</span>
      <Icon class="arrow-icon" :class="{ 'arrow-up': visible }" :icon="ArrowDown" />
    </div>

    <!-- 浮层选择器 -->
    <div v-if="visible" class="dropdown-overlay" @click="closeDropdown">
      <div class="dropdown-content" @click.stop>
        <div class="option-list">
          <div
            v-for="option in options"
            :key="option.value"
            class="option-item"
            :class="{ 'selected': modelValue === option.value }"
            @click="selectOption(option.value)"
          >
            <span class="option-text">{{ option.label }}</span>
            <svg
              v-if="modelValue === option.value"
              class="check-icon"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M3.55268 10.1795C3.84557 9.88414 4.32044 9.88414 4.61334 10.1795L7.58036 13.1715L15.3834 5.22418C15.6748 4.92736 16.1497 4.92498 16.444 5.21886C16.7384 5.51274 16.7407 5.9916 16.4493 6.28842L8.11597 14.7758C7.97556 14.9188 7.78443 14.9995 7.58487 15C7.38531 15.0005 7.19379 14.9208 7.05268 14.7785L3.55268 11.2491C3.25978 10.9537 3.25978 10.4749 3.55268 10.1795Z"
                fill="#3077F1"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { Icon } from '@xhs/reds-h5-next'
  import { ArrowDown } from '@xhs/reds-h5/icons'
  import { PriceType, type PriceTypeSelectorProps } from './types'

  // 选项配置
  const options = [
    { value: PriceType.FIXED, label: '固定价', display: '元' },
    { value: PriceType.STARTING, label: '起售价', display: '元起' }
  ]

  const props = withDefaults(defineProps<PriceTypeSelectorProps>(), {
    modelValue: PriceType.FIXED
  })

  const emit = defineEmits<{
    'update:modelValue': [value: number]
  }>()

  // 响应式数据
  const visible = ref(false)

  // 计算属性
  const displayText = computed(() => {
    const option = options.find(opt => opt.value === props.modelValue)
    return option?.display || '元'
  })

  // 方法
  const toggleDropdown = () => {
    visible.value = !visible.value
  }

  const closeDropdown = () => {
    visible.value = false
  }

  const selectOption = (value: number) => {
    emit('update:modelValue', value)
    closeDropdown()
  }
</script>

<style scoped lang="stylus">
.price-type-selector
  position relative
  display inline-block

.selector-trigger
  display flex
  align-items center
  justify-content space-between
  padding 8px 12px
  background-color #fff
  border 1px solid #e6e6e6
  border-radius 4px
  cursor pointer
  min-width 80px
  transition all 0.2s ease

  &:hover
    border-color #d9d9d9

  &:active
    border-color #1890ff

.display-text
  font-size 14px
  color #333
  line-height 20px

.arrow-icon
  margin-left 8px
  font-size 12px
  color #999
  transition transform 0.2s ease

  &.arrow-up
    transform rotate(180deg)

.dropdown-overlay
  position fixed
  top 0
  left 0
  right 0
  bottom 0
  z-index 1000
  background-color rgba(0, 0, 0, 0.3)

.dropdown-content
  position absolute
  top 100%
  left 0
  right 0
  margin-top 4px
  background-color #fff
  border-radius 8px
  box-shadow 0 4px 12px rgba(0, 0, 0, 0.15)
  overflow hidden
  z-index 1001

.option-list
  padding 8px 0

.option-item
  display flex
  align-items center
  justify-content space-between
  padding 12px 16px
  cursor pointer
  transition background-color 0.2s ease

  &:hover
    background-color #f5f5f5

  &.selected
    background-color #f0f8ff

.option-text
  font-size 14px
  color #333
  line-height 20px

.check-icon
  width 20px
  height 20px
  color #3077F1
</style>
