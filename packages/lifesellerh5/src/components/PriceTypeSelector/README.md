# PriceTypeSelector 价格类型选择组件

价格类型选择组件，支持固定价和起售价两种类型的选择。

## 功能特性

- ✅ 支持 v-model 双向绑定
- ✅ 默认状态显示对应的价格单位文字（"元" 或 "元起"）
- ✅ 点击展开浮层选择器，手动实现
- ✅ 选中项显示对勾图标（使用 assets/svg/check.svg）
- ✅ 自包含状态管理，无需外部状态控制
- ✅ 响应式设计，适配移动端触摸交互
- ✅ 使用 @xhs/reds-h5-next 组件库
- ✅ 遵循 Vue 3 Composition API 模式

## 使用方法

### 基础用法

```vue
<template>
  <div>
    <PriceTypeSelector v-model="priceType" />
    <p>当前选择的价格类型：{{ priceType }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PriceTypeSelector from '~/components/PriceTypeSelector/index.vue'

const priceType = ref(0) // 0=固定价，1=起售价
</script>
```

### 在表单中使用

```vue
<template>
  <div class="form-item">
    <label>价格类型：</label>
    <PriceTypeSelector v-model="formData.priceType" />
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import PriceTypeSelector from '~/components/PriceTypeSelector/index.vue'

const formData = reactive({
  priceType: 0, // 默认固定价
  price: 100
})
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 当前选中的价格类型 | number | 0 |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 价格类型改变时触发 | (value: number) |

### 价格类型枚举

```typescript
enum PriceType {
  FIXED = 0,    // 固定价 - 显示为"元"
  STARTING = 1  // 起售价 - 显示为"元起"
}
```

## 样式定制

组件使用 Stylus 编写样式，支持以下 CSS 变量定制：

- 触发器边框颜色
- 浮层背景色
- 选中状态背景色
- 文字颜色等

## 文件结构

```
PriceTypeSelector/
├── index.vue          # 主组件文件
├── index.ts           # 导出文件
├── types.ts           # 类型定义
├── example.vue        # 使用示例
├── test.vue           # 测试页面
└── README.md          # 文档说明
```

## 注意事项

1. 组件依赖 `@xhs/reds-h5-next` 组件库的 Icon 组件
2. 对勾图标直接内嵌 SVG，使用项目中 `assets/svg/check.svg` 的路径数据
3. 浮层使用固定定位，确保在各种布局中正常显示
4. 支持点击外部区域关闭浮层
5. 组件完全自包含，无需额外的样式或脚本依赖

## 技术实现

- 使用 Vue 3 Composition API
- TypeScript 类型安全
- Stylus 样式预处理器
- 响应式数据绑定
- 事件处理和状态管理
