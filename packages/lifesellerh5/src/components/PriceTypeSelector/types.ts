/**
 * 价格类型枚举
 */
export enum PriceType {
  FIXED = 0,    // 固定价 - 显示为"元"
  STARTING = 1  // 起售价 - 显示为"元起"
}

/**
 * 价格类型选项接口
 */
export interface PriceTypeOption {
  value: PriceType
  label: string
  display: string
}

/**
 * 组件 Props 接口
 */
export interface PriceTypeSelectorProps {
  modelValue?: number
}

/**
 * 组件 Emits 接口
 */
export interface PriceTypeSelectorEmits {
  'update:modelValue': [value: number]
}
