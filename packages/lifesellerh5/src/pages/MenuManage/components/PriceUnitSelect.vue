<template>
  <div class="price-type-selector">
    <!-- 默认显示区域 -->
    <div class="selector-trigger" @click="toggleDropdown">
      <span class="display-text">{{ displayText }}</span>
      <OnixIcon class="arrow-icon" icon="icon-right2" size="16" />
    </div>

    <!-- 浮层选择器 -->
    <div v-if="visible" class="dropdown-overlay" @click="closeDropdown">
      <div class="dropdown-content" @click.stop>
        <div
          v-for="option in options"
          :key="option.value"
          class="option-item"
          :class="{ 'selected': currentValue === option.value }"
          @click="selectOption(option.value)"
        >
          <span class="option-text">{{ option.label }}</span>
          <OnixIcon v-if="currentValue === option.value" icon="check" size="20" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import OnixIcon from '@xhs/onix-icon'
  import '~/assets/svg/icon-right2.svg'
  import '~/assets/svg/check.svg'

  /**
   * 价格类型枚举
   */
  enum PriceType {
    FIXED = 0, // 固定价 - 显示为"元"
    STARTING = 1 // 起售价 - 显示为"元起"
  }

  /**
   * 价格类型选项接口
   */
  interface PriceTypeOption {
    value: PriceType
    label: string
    display: string
  }

  // 选项配置
  const options:PriceTypeOption[] = [
    { value: PriceType.FIXED, label: '固定价', display: '元' },
    { value: PriceType.STARTING, label: '起售价', display: '元起' }
  ]

  const props = defineProps<{
    modelValue?: number
  }>()

  const emit = defineEmits<{
    'update:modelValue': [value: number] // 价格类型
  }>()

  // 响应式数据
  const visible = ref(false)

  // 计算属性 - 当前选中的值，支持默认值
  const currentValue = computed(() => props.modelValue ?? 0)

  // 计算属性 - 显示文本
  const displayText = computed(() => {
    const option = options.find(opt => opt.value === currentValue.value)
    return option?.display || '元'
  })

  // 方法
  const toggleDropdown = () => {
    visible.value = !visible.value
  }

  const closeDropdown = () => {
    visible.value = false
  }

  const selectOption = (value: number) => {
    emit('update:modelValue', value)
    closeDropdown()
  }
</script>

<style scoped lang="stylus">
.price-type-selector
  position relative
  display inline-block

.selector-trigger
  display flex
  align-items center
  text-wrap: nowrap
  gap 4px
.display-text
  font-size 16px
  font-weight 400
  line-height 24px
  color rgba(0, 0, 0, 0.8)

.dropdown-overlay
  position fixed
  top 0
  left 0
  right 0
  bottom 0
  z-index 1000
  background transparent

.dropdown-content
  position absolute
  top 180px
  right 20px
  width 102px
  height 88px
  border-radius 12px
  background rgba(255, 255, 255, 0.99)
  box-shadow 0px 4px 20px 0px rgba(0, 0, 0, 0.12)
  z-index 1001

.option-item
  display flex
  align-items center
  justify-content space-between
  padding 12px

.option-text
  font-size 14px
  font-weight 500
  line-height 18px
  color rgba(0, 0, 0, 0.8)

.check-icon
  width 20px
  height 20px
  color #3077F1
</style>
