# PriceUnitSelect v-model 数据绑定问题诊断指南

## 🚨 问题描述
父组件设置 `priceType = ref(1)`，但在 PriceUnitSelect 组件内部，`props.modelValue` 是 `undefined` 而不是 `1`。

## 🔍 诊断步骤

### 1. 使用测试组件进行诊断
```vue
<!-- 在你的页面中引入测试组件 -->
<PriceUnitSelectTest />
```

### 2. 打开浏览器控制台
查看以下调试信息：
- `=== 父组件初始值 ===` - 父组件的初始值
- `PriceUnitSelect - props.modelValue 变化:` - props 接收情况
- `PriceUnitSelect - 组件挂载时 props.modelValue:` - 挂载时的状态
- `PriceUnitSelect - currentValue:` - 计算属性的值

### 3. 检查常见问题

#### 问题1: 父组件初始化时序
```javascript
// ❌ 错误：异步设置初始值
const priceType = ref()
setTimeout(() => {
  priceType.value = 1 // 这会导致初始时 props.modelValue 是 undefined
}, 0)

// ✅ 正确：直接设置初始值
const priceType = ref(1)
```

#### 问题2: v-model 使用方式
```vue
<!-- ✅ 正确的使用方式 -->
<PriceUnitSelect v-model="priceType" />

<!-- ❌ 错误：不要同时使用 v-model 和 :modelValue -->
<PriceUnitSelect v-model="priceType" :modelValue="1" />
```

#### 问题3: 响应式数据类型
```javascript
// ✅ 正确：使用 ref
const priceType = ref(1)

// ❌ 错误：使用普通变量
let priceType = 1
```

## 🔧 解决方案

### 方案1: 确保正确的初始化
```vue
<script setup>
import { ref } from 'vue'

// 直接设置初始值，不要异步设置
const priceType = ref(1) // 起售价
// 或
const priceType = ref(0) // 固定价
</script>

<template>
  <PriceUnitSelect v-model="priceType" />
</template>
```

### 方案2: 处理异步初始化
如果必须异步设置初始值：
```vue
<script setup>
import { ref, nextTick } from 'vue'

const priceType = ref(0) // 先设置默认值

// 异步设置时使用 nextTick
const loadData = async () => {
  const data = await fetchData()
  await nextTick()
  priceType.value = data.priceType
}
</script>
```

### 方案3: 使用 watch 监听变化
```vue
<script setup>
import { ref, watch } from 'vue'

const priceType = ref(1)

// 监听变化以确保数据传递正确
watch(priceType, (newValue) => {
  console.log('父组件 priceType 变化:', newValue)
}, { immediate: true })
</script>
```

## 🧪 验证修复效果

使用测试组件验证以下场景：

1. **默认值测试**：不传初始值时应显示"元"
2. **初始值为1测试**：应显示"元起"，下拉框中"起售价"有对勾
3. **动态切换测试**：修改值时界面应立即响应
4. **直接传值测试**：使用 `:modelValue` 直接传值应正常工作

## 📋 检查清单

- [ ] 父组件使用 `ref()` 创建响应式数据
- [ ] 初始值直接在 `ref()` 中设置，不是异步设置
- [ ] 使用 `v-model` 而不是手动绑定 `:modelValue` 和 `@update:modelValue`
- [ ] 控制台没有 Vue 警告或错误
- [ ] 组件能正确显示对应的文本和选中状态

## 🔄 如果问题仍然存在

1. 检查 Vue 版本兼容性
2. 确认没有其他组件或指令干扰
3. 尝试在最简单的环境中复现问题
4. 查看完整的错误堆栈信息

## 📞 获取帮助

如果按照以上步骤仍无法解决问题，请提供：
1. 控制台的完整调试输出
2. 父组件的完整代码
3. Vue 版本信息
4. 任何相关的错误信息
