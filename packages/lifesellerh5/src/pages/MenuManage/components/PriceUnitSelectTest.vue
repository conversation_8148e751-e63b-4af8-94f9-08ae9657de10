<template>
  <div class="test-container">
    <h3>PriceUnitSelect v-model 数据绑定测试</h3>

    <div class="test-case">
      <h4>测试1: 默认值 (不传 modelValue)</h4>
      <PriceUnitSelect v-model="priceType1" />
      <p>父组件值: {{ priceType1 }} ({{ typeof priceType1 }})</p>
      <p>期望: undefined → 0 (固定价，显示"元")</p>
    </div>

    <div class="test-case">
      <h4>测试2: 初始值为 1 (起售价) - 关键测试</h4>
      <PriceUnitSelect v-model="priceType2" />
      <p>父组件值: {{ priceType2 }} ({{ typeof priceType2 }})</p>
      <p>期望: 1 (起售价，显示"元起")</p>
      <button @click="setPriceType2To1">设置为起售价(1)</button>
      <button @click="setPriceType2To0">设置为固定价(0)</button>
      <button @click="resetPriceType2">重置为undefined</button>
    </div>

    <div class="test-case">
      <h4>测试3: 动态切换</h4>
      <PriceUnitSelect v-model="priceType3" />
      <p>父组件值: {{ priceType3 }} ({{ typeof priceType3 }})</p>
      <button @click="togglePriceType3">切换价格类型</button>
    </div>

    <div class="test-case">
      <h4>测试4: 直接传值测试</h4>
      <PriceUnitSelect :modelValue="1" @update:modelValue="handleUpdate" />
      <p>直接传值: 1, 更新值: {{ directValue }}</p>
    </div>

    <div class="debug-info">
      <h4>调试信息</h4>
      <p>打开浏览器控制台查看详细的 props.modelValue 传递情况</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import PriceUnitSelect from './PriceUnitSelect.vue'

// 测试用的响应式数据
const priceType1 = ref() // 不设置初始值，测试默认值
const priceType2 = ref(1) // 设置为起售价 - 这是关键测试
const priceType3 = ref(0) // 设置为固定价
const directValue = ref()

// 调试：打印初始值
onMounted(() => {
  console.log('=== 父组件初始值 ===')
  console.log('priceType1:', priceType1.value, typeof priceType1.value)
  console.log('priceType2:', priceType2.value, typeof priceType2.value)
  console.log('priceType3:', priceType3.value, typeof priceType3.value)
})

// 测试方法
const setPriceType2To1 = () => {
  console.log('设置 priceType2 为 1')
  priceType2.value = 1
}

const setPriceType2To0 = () => {
  console.log('设置 priceType2 为 0')
  priceType2.value = 0
}

const resetPriceType2 = () => {
  console.log('重置 priceType2 为 undefined')
  priceType2.value = undefined
}

const togglePriceType3 = () => {
  const newValue = priceType3.value === 0 ? 1 : 0
  console.log('切换 priceType3 从', priceType3.value, '到', newValue)
  priceType3.value = newValue
}

const handleUpdate = (value: number) => {
  console.log('直接传值组件更新:', value)
  directValue.value = value
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 600px;
}

.test-case {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-case h4 {
  margin-top: 0;
  color: #333;
}

.test-case p {
  margin: 10px 0;
  font-family: monospace;
}

button {
  margin-right: 10px;
  padding: 5px 10px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}
</style>
