<template>
  <div class="test-container">
    <h3>PriceUnitSelect 测试</h3>
    
    <div class="test-case">
      <h4>测试1: 默认值 (不传 modelValue)</h4>
      <PriceUnitSelect v-model="priceType1" />
      <p>当前值: {{ priceType1 }}</p>
      <p>期望: 0 (固定价，显示"元")</p>
    </div>

    <div class="test-case">
      <h4>测试2: 传入值 1 (起售价)</h4>
      <PriceUnitSelect v-model="priceType2" />
      <p>当前值: {{ priceType2 }}</p>
      <p>期望: 1 (起售价，显示"元起")</p>
      <button @click="setPriceType2To1">设置为起售价(1)</button>
      <button @click="setPriceType2To0">设置为固定价(0)</button>
    </div>

    <div class="test-case">
      <h4>测试3: 动态切换</h4>
      <PriceUnitSelect v-model="priceType3" />
      <p>当前值: {{ priceType3 }}</p>
      <button @click="togglePriceType3">切换价格类型</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PriceUnitSelect from './PriceUnitSelect.vue'

// 测试用的响应式数据
const priceType1 = ref() // 不设置初始值，测试默认值
const priceType2 = ref(1) // 设置为起售价
const priceType3 = ref(0) // 设置为固定价

// 测试方法
const setPriceType2To1 = () => {
  priceType2.value = 1
}

const setPriceType2To0 = () => {
  priceType2.value = 0
}

const togglePriceType3 = () => {
  priceType3.value = priceType3.value === 0 ? 1 : 0
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 600px;
}

.test-case {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-case h4 {
  margin-top: 0;
  color: #333;
}

.test-case p {
  margin: 10px 0;
  font-family: monospace;
}

button {
  margin-right: 10px;
  padding: 5px 10px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}
</style>
