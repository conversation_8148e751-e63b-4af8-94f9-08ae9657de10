<template>
  <div class="dish-create">
    <NavigationBar
      :title="isEdit ? '编辑菜品' : '添加菜品'"
      left-arrow
      @click-left="onBack"
    />

    <div class="form-container">
      <Form
        ref="formRef"
        :data="formData"
        :rules="formRules"
        :align="FormType.ALIGN.RIGHT"
        :show-submit="false"
        @submit="onSubmit"
        @error="onFormError"
      >
        <!-- 菜品名称 -->
        <FormItem
          label="菜品名称"
          name="name"
          :value="formData.name"
          class="form-item"
          required
        >
          <TextField
            v-model="formData.name"
            placeholder="请输入菜品名称"
            :maxlength="15"
            clearable
            input-align="right"
            :is-form-item="true"
            style="padding: 0 !important;"
          />
        </FormItem>

        <!-- 菜品价格 -->
        <FormItem
          label="菜品价格"
          name="price"
          :value="formData.price"
          class="form-item"
          required
        >
          <div class="price-input-wrapper">
            <TextField
              v-model="formData.price"
              placeholder="请输入价格"
              type="number"
              input-align="right"
              :is-form-item="true"
              style="padding: 0 !important;"
            />
            <PriceUnitSelect v-model="formData.priceType" />
          </div>
        </FormItem>

        <!-- 菜品图 -->
        <FormItem
          label="菜品图"
          name="image"
          :value="formData.image"
          class="form-item"
          required
          :layout="FormType.LAYOUT.VERTICAL"
        >
          <div class="image-upload-container">
            <Upload
              v-model="formData.image"
              :max-count="1"
              :max-size="4 * 1024 * 1024"
              :is-preview="false"
              :prohibit-operation="false"
              @update:model-value="handleImageListUpdate"
            />
            <div class="upload-tips">建议使用高清优质图，图片不得超过4M，建议分辨率为900x900，图片比例为1:1</div>
          </div>
        </FormItem>

        <!-- 是否添加为招牌菜 -->
        <FormItem
          label="是否添加为招牌菜"
          name="isSpecialty"
          :value="formData.isSpecialty"
          class="form-item form-item-specialty"
        >
          <div class="specialty-section">
            <div class="specialty-header">
              <Icon name="question-o" size="16" color="#c8c9cc" />
            </div>
            <Switch
              v-model="formData.isSpecialty"
              :disabled="!canSetSpecialty"
              size="24"
              @change="onSpecialtyChange"
            />
          </div>
        </FormItem>
      </Form>
    </div>

    <!-- 底部按钮 -->
    <div class="footer-button">
      <Button
        type="primary"
        block
        round
        size="large"
        :disabled="!isFormValid"
        :loading="submitting"
        @click="handleSubmit"
      >
        {{ isEdit ? '保存修改' : '确认添加' }}
      </Button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import {
    ref, computed, onMounted, onBeforeUnmount
  } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { useStore } from 'vuex'
  import {
    NavigationBar,
    Button,
    TextField,
    Icon,
    Switch,
    showToast,
    Form,
    FormItem,
    FormType
  } from '@xhs/reds-h5-next'
  import Upload from './components/ImageUpload/index.vue'
  import { postUpsertMenuDish } from '~/services/edith_post_upsert_menu_dish'
  import PriceUnitSelect from './components/PriceUnitSelect.vue'
  import {
    validateDish,
    getSpecialtyDishCount,
    checkDishDuplicate as checkDishDuplicateService
  } from '~/services/menuManage'
  import {
    IDishList,
    PriceType,
    SpecialtyType,
    ResourceType
  } from '~/types/menu'

  const router = useRouter()
  const route = useRoute()
  const store = useStore()
  const submitting = ref(false)
  const formRef = ref()

  // 从路由获取参数
  const dishId = computed(() => route.query.id as string)
  const groupId = computed(() => route.query.groupId as string)
  const poiId = computed(() => route.query.poiId as string)
  const isEdit = computed(() => !!dishId.value)

  // 从 store 获取编辑菜品数据
  const editDishData = computed(() => store.getters['menuManage/dish'] as IDishList | null)

  interface FormData {
    name: string
    priceItem: {
      price: string
      priceType: number
    }
    image: any[]
    isSpecialty: boolean
  }

  const formData = ref<FormData>({
    name: '',
    priceItem: {
      price: '',
      priceType: 0
    },
    image: [],
    isSpecialty: false
  })

  const originalFormData = ref<FormData | null>(null)
  const specialtyDishCount = ref(0)

  // 表单验证规则
  const formRules = {
    name: [
      {
        type: 'string',
        required: true,
        message: '请输入菜品名称'
      },
      {
        type: 'string',
        validator: (rule: any, value: string) => value.length <= 15,
        message: '菜品名称不能超过15个字符'
      }
    ],
    price: [
      {
        type: 'string',
        required: true,
        message: '请输入菜品价格'
      },
      {
        type: 'string',
        validator: (rule: any, value: string) => /^\d+(\.\d{1})?$/.test(value),
        message: '价格格式不正确，最多保留1位小数'
      },
      {
        type: 'string',
        validator: (rule: any, value: string) => parseFloat(value) > 0,
        message: '价格必须大于0'
      }
    ],
    image: [
      {
        type: 'array',
        validator: (rule: any, value: any[]) => value && value.length > 0,
        message: '请上传菜品图片'
      }
    ]
  }

  // 是否可以设置为招牌菜
  const canSetSpecialty = computed(() => {
    if (formData.value.isSpecialty) return true // 已经是招牌菜可以取消
    return specialtyDishCount.value < 10
  })

  const handleImageListUpdate = (value: any) => {
    console.log('value', value)
  }

  // 表单验证
  const isFormValid = computed(() => formData.value.name.trim() !== ''
    && formData.value.price !== ''
    && formData.value.image.length > 0
    && parseFloat(formData.value.price) > 0
    && /^\d+(\.\d{1})?$/.test(formData.value.price))

  // 检测是否有变更
  const hasChanges = computed(() => {
    if (!originalFormData.value) return false
    return JSON.stringify(formData.value) !== JSON.stringify(originalFormData.value)
  })

  // 表单错误处理
  const onFormError = (errors: any, fields: any) => {
    console.error('表单验证失败:', errors, fields)
    // 显示第一个错误信息
    if (errors && errors.length > 0) {
      showToast(errors[0].message || '表单验证失败')
    }
  }

  // 表单提交处理
  const handleSubmit = async () => {
    if (!formRef.value) return
    try {
      await formRef.value.handleSubmit()
    } catch (error) {
      console.error('表单提交失败:', error)
    }
  }

  // 设置价格类型
  const setPriceType = (type: 'fixed' | 'starting') => {
    formData.value.priceType = type
  }

  // 招牌菜切换
  const onSpecialtyChange = (value: boolean) => {
    if (value && specialtyDishCount.value >= 10) {
      showToast('最多设置10个招牌菜')
      formData.value.isSpecialty = false
      return
    }
    formData.value.isSpecialty = value
  }

  // 获取招牌菜数量
  const fetchSpecialtyCount = async () => {
    try {
      specialtyDishCount.value = await getSpecialtyDishCount(poiId.value)
    } catch (error) {
      console.error('获取招牌菜数量失败:', error)
    }
  }

  // 初始化表单数据
  const initializeFormData = () => {
    if (editDishData.value) {
      // 使用传递的菜品数据
      const dish = editDishData.value
      formData.value = {
        name: dish.dishName,
        price: dish.priceItem.dishPrice.toString(),
        priceType: dish.priceItem.priceType === PriceType.FIXED ? 'fixed' : 'starting',
        image: dish.dishResourceList,
        isSpecialty: dish.specialty === SpecialtyType.YES
      }
      originalFormData.value = JSON.parse(JSON.stringify(formData.value))
    }
  }

  // 提交表单
  const onSubmit = async () => {
    if (!isFormValid.value) return

    submitting.value = true

    try {
      // 菜品去重检查
      const isDuplicate = await checkDishDuplicateService({
        name: formData.value.name,
        price: parseFloat(formData.value.price),
        excludeId: dishId.value
      })

      if (isDuplicate) {
        showToast('菜品重复')
        return
      }

      // 菜品审核 - 修复图片类型问题
      const imageUrl = formData.value.image && formData.value.image.length > 0
        ? (typeof formData.value.image[0] === 'string' ? formData.value.image[0] : formData.value.image[0]?.url || '')
        : ''

      const auditResult = await validateDish({
        name: formData.value.name,
        image: imageUrl
      })

      if (!auditResult.isValid) {
        showToast(`当前菜品${auditResult.failedField}审核未通过，请修改后再添加`)
        return
      }

      // 构造保存数据
      const dishData = {
        dishId: dishId.value,
        dishName: formData.value.name,
        priceItem: {
          dishPrice: parseFloat(formData.value.price),
          priceType: formData.value.priceType === 'fixed' ? 0 : 1 // 0:fixed, 1:starting
        },
        dishResourceList: formData.value.image && formData.value.image.length > 0 ? [{
          resourceId: `${dishId.value || Date.now()}_img`,
          type: ResourceType.IMAGE,
          resourceInfo: imageUrl, // 使用 resourceInfo 存储图片URL
          mediaId: typeof formData.value.image[0] === 'string' ? formData.value.image[0] : formData.value.image[0]?.id || '',
          sortOrder: 0
        }] : [],
        specialty: formData.value.isSpecialty ? 1 : 0, // 0-否，1-是
        sortOrder: editDishData.value?.sortOrder || 0,
        dishSource: 0, // 0-商家上传
        recommendSortOrder: formData.value.isSpecialty ? editDishData.value?.recommendSortOrder || 0 : 0
      }

      // 调用保存接口
      await postUpsertMenuDish({
        userId: 'current_user_id', // 实际使用时需要从用户信息获取
        poiId: poiId.value,
        menuGroup: {
          groupId: groupId.value,
          dish: dishData
        }
      })

      showToast(isEdit.value ? '菜品修改成功' : '菜品已添加')
      router.back()
    } catch (error) {
      console.error('保存失败:', error)
      showToast('保存失败，请重试')
    } finally {
      submitting.value = false
    }
  }

  // 返回处理
  const onBack = async () => {
    if (hasChanges.value) {
      showToast('当前编辑菜品内容尚未保存')
      return
    }
    router.back()
  }

  // 页面离开前检查
  const beforeUnload = (e: BeforeUnloadEvent) => {
    if (hasChanges.value) {
      e.preventDefault()
      e.returnValue = ''
    }
  }

  onMounted(() => {
    fetchSpecialtyCount()
    initializeFormData()
    window.addEventListener('beforeunload', beforeUnload)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', beforeUnload)
    // 清理 store 中的编辑数据
    store.dispatch('menuManage/clearDish')
  })
</script>

<style lang="stylus" scoped>
.dish-create
  min-height 100vh
  background-color #f7f8fa

.form-container
  padding 16px 16px 120px

  :deep(.reds-form)
    background transparent

  .form-item
    background white
    border-radius 12px
    padding 16px
    margin-bottom 12px

    &:last-child
      margin-bottom 0

    &.form-item-specialty
      .specialty-tips
        font-size 12px
        color #969799
        line-height 1.4
        margin-top 8px

:deep(.reds-form-item)
  padding 0
  margin-bottom 0
  border-bottom none

:deep(.reds-form-item-label)
  font-size 16px
  color #323233
  font-weight 500

:deep(.reds-form-item-required)
  color #FF2442

.price-input-wrapper
  display flex
  align-items center
  gap 8px

  .price-unit
    font-size 16px
    color #323233
    white-space nowrap

.price-type-section
  display flex
  gap 24px
  margin-top 16px

  .price-type-option
    display flex
    align-items center
    gap 8px
    cursor pointer

    span
      font-size 16px
      color #323233

.image-upload-container
  .dish-uploader
    margin-bottom 12px

  .upload-area
    width 80px
    height 80px
    border 2px dashed #dcdee0
    border-radius 8px
    display flex
    align-items center
    justify-content center
    background #fafafa

  .upload-tips
    font-size 12px
    color rgba(0, 0, 0, 0.45)
    line-height 18px
    margin-top 12px

.specialty-section
  display flex
  align-items center
  justify-content space-between

  .specialty-header
    display flex
    align-items center
    gap 4px

.error-message
  color #ee0a24
  font-size 12px
  margin-top 4px

.footer-button
  position fixed
  bottom 0
  left 0
  right 0
  padding 16px
  background white
  border-top 1px solid #ebedf0
  z-index 100
</style>
